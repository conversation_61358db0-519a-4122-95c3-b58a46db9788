#!/bin/bash

# MongoDB Setup Script for Showfer Project
# This script helps set up MongoDB alongside PostgreSQL

set -e

echo "🚀 Setting up MongoDB for Showfer Project"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_status "Docker and Docker Compose are installed"
}

# Check if MongoDB is already running locally
check_local_mongodb() {
    if nc -z localhost 27017 2>/dev/null; then
        print_warning "MongoDB is already running on port 27017"
        read -p "Do you want to continue with Docker setup? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Using existing MongoDB installation"
            return 1
        fi
    fi
    return 0
}

# Create environment file
create_env_file() {
    print_step "Creating .env file with MongoDB configuration"
    
    if [ -f ".env" ]; then
        print_warning ".env file already exists. Creating backup..."
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    cat > .env << EOF
# PostgreSQL Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=showfer
DB_SSLMODE=disable

# MongoDB Database Configuration
MONGO_URI=***********************************************************************
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DATABASE=showfer_mongo
MONGO_USERNAME=admin
MONGO_PASSWORD=admin123
MONGO_AUTH_SOURCE=admin

# Backblaze Configuration (optional)
# B2_KEY_ID=your_key_id
# B2_APP_KEY=your_app_key
# B2_DEFAULT_BUCKET=your_bucket_name
EOF
    
    print_status ".env file created successfully"
}

# Start MongoDB with Docker
start_mongodb_docker() {
    print_step "Starting MongoDB with Docker Compose"
    
    # Create data directories
    mkdir -p data/mongodb_backup
    mkdir -p data/postgres_backup
    
    # Start only MongoDB and related services
    docker-compose -f docker-compose.mongodb.yml up -d mongodb mongo-express
    
    print_status "MongoDB and Mongo Express started"
    print_status "MongoDB: mongodb://localhost:27017"
    print_status "Mongo Express UI: http://localhost:8081 (admin/admin123)"
}

# Wait for MongoDB to be ready
wait_for_mongodb() {
    print_step "Waiting for MongoDB to be ready..."
    
    for i in {1..30}; do
        if docker exec showfer_mongodb mongosh --eval "db.adminCommand('ping')" --quiet > /dev/null 2>&1; then
            print_status "MongoDB is ready!"
            return 0
        fi
        echo -n "."
        sleep 2
    done
    
    print_error "MongoDB failed to start within 60 seconds"
    return 1
}

# Test MongoDB connection
test_mongodb_connection() {
    print_step "Testing MongoDB connection"
    
    # Test with mongosh if available
    if command -v mongosh &> /dev/null; then
        if mongosh "***********************************************************************" --eval "db.runCommand({ping: 1})" --quiet > /dev/null 2>&1; then
            print_status "MongoDB connection test successful"
        else
            print_warning "MongoDB connection test failed"
        fi
    else
        print_warning "mongosh not found, skipping connection test"
    fi
}

# Install Go dependencies
install_dependencies() {
    print_step "Installing Go dependencies"
    
    cd backend
    go mod tidy
    cd ..
    
    print_status "Go dependencies installed"
}

# Build and test the application
test_build() {
    print_step "Testing application build"
    
    cd backend
    if go build -o /tmp/showfer-test .; then
        print_status "Application builds successfully with MongoDB support"
        rm -f /tmp/showfer-test
    else
        print_error "Application build failed"
        return 1
    fi
    cd ..
}

# Show next steps
show_next_steps() {
    echo
    echo "🎉 MongoDB setup completed successfully!"
    echo "======================================"
    echo
    echo "Next steps:"
    echo "1. Start your application:"
    echo "   cd backend && go run main.go"
    echo
    echo "2. Check database status:"
    echo "   curl -H 'Authorization: Bearer <token>' http://localhost:8080/api/v1/database/status"
    echo
    echo "3. Access MongoDB UI:"
    echo "   http://localhost:8081 (username: admin, password: admin123)"
    echo
    echo "4. Migrate existing data (optional):"
    echo "   cd backend/cmd/migrate_to_mongo && go run main.go -dry-run"
    echo
    echo "5. Read the integration guide:"
    echo "   cat backend/MONGODB_INTEGRATION.md"
    echo
    print_status "Setup complete! 🚀"
}

# Main execution
main() {
    print_step "Starting MongoDB setup for Showfer"
    
    # Check prerequisites
    check_docker
    
    # Check for existing MongoDB
    if ! check_local_mongodb; then
        print_status "Using existing MongoDB installation"
        create_env_file
        install_dependencies
        test_build
        show_next_steps
        return 0
    fi
    
    # Setup process
    create_env_file
    start_mongodb_docker
    wait_for_mongodb
    test_mongodb_connection
    install_dependencies
    test_build
    show_next_steps
}

# Handle script interruption
trap 'print_error "Setup interrupted"; exit 1' INT TERM

# Run main function
main "$@"
