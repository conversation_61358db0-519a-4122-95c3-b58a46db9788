package config

import (
	"context"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Global MongoDB instance
var globalMongoDB *mongo.Database

// MongoDBConfig holds MongoDB configuration
type MongoDBConfig struct {
	URI        string
	Database   string
	Host       string
	Port       string
	Username   string
	Password   string
	AuthSource string
}

// GetMongoDBConfig reads MongoDB configuration from environment variables
func GetMongoDBConfig() MongoDBConfig {
	config := MongoDBConfig{
		URI:        getEnv("MONGO_URI", ""),
		Database:   getEnv("MONGO_DATABASE", "showfer_mongo"),
		Host:       getEnv("MONGO_HOST", "localhost"),
		Port:       getEnv("MONGO_PORT", "27017"),
		Username:   getEnv("MONGO_USERNAME", ""),
		Password:   getEnv("MONGO_PASSWORD", ""),
		AuthSource: getEnv("MONGO_AUTH_SOURCE", "admin"),
	}

	// If URI is not provided, construct it from individual components
	if config.URI == "" {
		if config.Username != "" && config.Password != "" {
			config.URI = fmt.Sprintf("mongodb://%s:%s@%s:%s/%s?authSource=%s",
				config.Username, config.Password, config.Host, config.Port, config.Database, config.AuthSource)
		} else {
			config.URI = fmt.Sprintf("mongodb://%s:%s/%s", config.Host, config.Port, config.Database)
		}
	}

	return config
}

// InitMongoDB initializes the MongoDB connection
func InitMongoDB() (*mongo.Database, error) {
	config := GetMongoDBConfig()

	// Set client options
	clientOptions := options.Client().ApplyURI(config.URI)

	// Set connection timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Connect to MongoDB
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	// Test the connection
	err = client.Ping(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to ping MongoDB: %w", err)
	}

	// Get database instance
	database := client.Database(config.Database)

	// Store database globally for access by other packages
	globalMongoDB = database

	log.Printf("MongoDB connected successfully to database: %s", config.Database)
	return database, nil
}

// GetMongoDB returns the global MongoDB database instance
func GetMongoDB() *mongo.Database {
	return globalMongoDB
}

// CreateMongoIndexes creates necessary indexes for MongoDB collections
func CreateMongoIndexes(db *mongo.Database) error {
	ctx := context.Background()

	// Create indexes for analytics collection
	analyticsCollection := db.Collection("analytics")
	analyticsIndexes := []mongo.IndexModel{
		{
			Keys: map[string]interface{}{
				"schedule_id": 1,
				"played_at":   -1,
			},
		},
		{
			Keys: map[string]interface{}{
				"content_path": 1,
			},
		},
		{
			Keys: map[string]interface{}{
				"play_type": 1,
				"played_at": -1,
			},
		},
		{
			Keys: map[string]interface{}{
				"played_at": -1,
			},
		},
	}

	_, err := analyticsCollection.Indexes().CreateMany(ctx, analyticsIndexes)
	if err != nil {
		return fmt.Errorf("failed to create analytics indexes: %w", err)
	}

	// Create indexes for logs collection
	logsCollection := db.Collection("logs")
	logsIndexes := []mongo.IndexModel{
		{
			Keys: map[string]interface{}{
				"timestamp": -1,
			},
		},
		{
			Keys: map[string]interface{}{
				"level":     1,
				"timestamp": -1,
			},
		},
		{
			Keys: map[string]interface{}{
				"source":    1,
				"timestamp": -1,
			},
		},
		{
			Keys: map[string]interface{}{
				"user_id": 1,
			},
		},
		{
			Keys: map[string]interface{}{
				"request_id": 1,
			},
		},
	}

	_, err = logsCollection.Indexes().CreateMany(ctx, logsIndexes)
	if err != nil {
		return fmt.Errorf("failed to create logs indexes: %w", err)
	}

	// Create indexes for schedules collection
	schedulesCollection := db.Collection("schedules")
	schedulesIndexes := []mongo.IndexModel{
		{
			Keys: map[string]interface{}{
				"short_id": 1,
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: map[string]interface{}{
				"name": 1,
			},
		},
		{
			Keys: map[string]interface{}{
				"created_at": -1,
			},
		},
	}

	_, err = schedulesCollection.Indexes().CreateMany(ctx, schedulesIndexes)
	if err != nil {
		return fmt.Errorf("failed to create schedules indexes: %w", err)
	}

	// Create indexes for file_metadata collection
	fileMetadataCollection := db.Collection("file_metadata")
	fileMetadataIndexes := []mongo.IndexModel{
		{
			Keys: map[string]interface{}{
				"file_path": 1,
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: map[string]interface{}{
				"content_type": 1,
			},
		},
		{
			Keys: map[string]interface{}{
				"created_at": -1,
			},
		},
	}

	_, err = fileMetadataCollection.Indexes().CreateMany(ctx, fileMetadataIndexes)
	if err != nil {
		return fmt.Errorf("failed to create file_metadata indexes: %w", err)
	}

	log.Println("MongoDB indexes created successfully")
	return nil
}

// CloseMongoDB closes the MongoDB connection
func CloseMongoDB() error {
	if globalMongoDB != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		return globalMongoDB.Client().Disconnect(ctx)
	}
	return nil
}
