package storage

import (
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/kurin/blazer/b2"
)

type BackblazeService struct {
	client *b2.Client
	keyID  string
	appKey string
	ctx    context.Context
}

type BucketInfo struct {
	Name      string    `json:"name"`
	ID        string    `json:"id"`
	CreatedAt time.Time `json:"created_at"`
}

type FileInfo struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Size        int64     `json:"size"`
	ContentType string    `json:"content_type"`
	UploadedAt  time.Time `json:"uploaded_at"`
	BucketID    string    `json:"bucket_id"`
	URL         string    `json:"url"`
}

func NewBackblazeService(keyID, appKey string) (*BackblazeService, error) {
	log.Printf("Creating new Backblaze service with Key ID: %s...", keyID[:10])

	ctx := context.Background()
	client, err := b2.NewClient(ctx, keyID, appKey)
	if err != nil {
		log.Printf("Failed to create B2 client: %v", err)
		return nil, fmt.Errorf("failed to create B2 client: %v", err)
	}

	log.Printf("Successfully created B2 client")
	return &BackblazeService{
		client: client,
		keyID:  keyID,
		appKey: appKey,
		ctx:    ctx,
	}, nil
}

func (s *BackblazeService) ListBuckets() ([]BucketInfo, error) {
	log.Printf("Listing Backblaze buckets...")
	buckets, err := s.client.ListBuckets(s.ctx)
	if err != nil {
		log.Printf("Failed to list buckets: %v", err)
		return nil, fmt.Errorf("failed to list buckets: %v", err)
	}

	var bucketInfos []BucketInfo
	for _, bucket := range buckets {
		bucketInfos = append(bucketInfos, BucketInfo{
			Name:      bucket.Name(),
			ID:        bucket.Name(), // Use bucket name as ID since ID() is not available
			CreatedAt: time.Now(),    // B2 API doesn't provide creation time
		})
	}

	log.Printf("Found %d buckets", len(bucketInfos))
	return bucketInfos, nil
}

func (s *BackblazeService) ListFiles(bucketName string) ([]FileInfo, error) {
	bucket, err := s.client.Bucket(s.ctx, bucketName)
	if err != nil {
		return nil, fmt.Errorf("failed to get bucket: %v", err)
	}

	iter := bucket.List(s.ctx, b2.ListHidden())
	var files []FileInfo

	for iter.Next() {
		f := iter.Object()
		attr, err := f.Attrs(s.ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to get file attributes: %v", err)
		}

		files = append(files, FileInfo{
			ID:          f.Name(),
			Name:        filepath.Base(f.Name()),
			Size:        attr.Size,
			ContentType: attr.ContentType,
			UploadedAt:  time.Now(), // B2 API doesn't provide upload time in a directly accessible way
			BucketID:    bucketName,
			URL:         f.URL(),
		})
	}
	if err := iter.Err(); err != nil {
		return nil, fmt.Errorf("failed to list files: %v", err)
	}

	return files, nil
}

func (s *BackblazeService) UploadFile(bucketName string, filePath string, location string) (*FileInfo, error) {
	bucket, err := s.client.Bucket(s.ctx, bucketName)
	if err != nil {
		return nil, fmt.Errorf("failed to get bucket: %v", err)
	}

	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// Create a unique path in B2 using the location and filename
	b2Path := strings.TrimPrefix(filepath.Join(location, filepath.Base(filePath)), "/")
	obj := bucket.Object(b2Path)

	writer := obj.NewWriter(s.ctx)
	if _, err := io.Copy(writer, file); err != nil {
		writer.Close()
		return nil, fmt.Errorf("failed to upload file: %v", err)
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close writer: %v", err)
	}

	attr, err := obj.Attrs(s.ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get file attributes: %v", err)
	}

	return &FileInfo{
		ID:          obj.Name(),
		Name:        filepath.Base(filePath),
		Size:        attr.Size,
		ContentType: attr.ContentType,
		UploadedAt:  time.Now(), // B2 API doesn't provide upload time in a directly accessible way
		BucketID:    bucketName,
		URL:         obj.URL(),
	}, nil
}

// GetDownloadURL returns a direct download URL for a file in Backblaze
func (s *BackblazeService) GetDownloadURL(bucketName string, filePath string) (string, error) {
	bucket, err := s.client.Bucket(s.ctx, bucketName)
	if err != nil {
		return "", fmt.Errorf("failed to get bucket: %v", err)
	}

	// Clean the file path (remove leading slash if present)
	cleanPath := strings.TrimPrefix(filePath, "/")
	obj := bucket.Object(cleanPath)

	// Get the download URL
	url := obj.URL()
	return url, nil
}

func (s *BackblazeService) DeleteFile(bucketName string, fileName string) error {
	bucket, err := s.client.Bucket(s.ctx, bucketName)
	if err != nil {
		return fmt.Errorf("failed to get bucket: %v", err)
	}

	obj := bucket.Object(fileName)
	if err := obj.Delete(s.ctx); err != nil {
		return fmt.Errorf("failed to delete file: %v", err)
	}

	return nil
}
