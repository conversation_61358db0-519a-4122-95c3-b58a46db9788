package database_manager

import (
	"database/sql"
	"log"
	"showfer-web/config"
	"showfer-web/repository"

	"go.mongodb.org/mongo-driver/mongo"
)

// DatabaseManager manages both PostgreSQL and MongoDB connections
type DatabaseManager struct {
	PostgresDB *sql.DB
	MongoDB    *mongo.Database
	
	// PostgreSQL repositories
	UsersRepo           *repository.UserRepository
	FilesRepo           *repository.FilesRepository
	RecorderRepo        *repository.RecorderRepository
	CodecSettingsRepo   *repository.CodecSettingsRepository
	GeneralSettingsRepo *repository.GeneralSettingsRepository
	
	// MongoDB repositories
	MongoAnalyticsRepo *repository.MongoAnalyticsRepository
	MongoLogsRepo      *repository.MongoLogsRepository
	
	// Flags to indicate which databases are available
	PostgresAvailable bool
	MongoAvailable    bool
}

// NewDatabaseManager creates a new database manager instance
func NewDatabaseManager() *DatabaseManager {
	return &DatabaseManager{}
}

// Initialize sets up both database connections and repositories
func (dm *DatabaseManager) Initialize() error {
	// Initialize PostgreSQL
	if err := dm.initializePostgreSQL(); err != nil {
		return err
	}

	// Initialize MongoDB (non-fatal if it fails)
	dm.initializeMongoDB()

	// Initialize repositories
	dm.initializeRepositories()

	return nil
}

// initializePostgreSQL sets up PostgreSQL connection
func (dm *DatabaseManager) initializePostgreSQL() error {
	db, err := config.InitDB()
	if err != nil {
		return err
	}

	dm.PostgresDB = db
	dm.PostgresAvailable = true
	log.Println("PostgreSQL initialized successfully")
	return nil
}

// initializeMongoDB sets up MongoDB connection
func (dm *DatabaseManager) initializeMongoDB() {
	mongoDB, err := config.InitMongoDB()
	if err != nil {
		log.Printf("Warning: Failed to initialize MongoDB: %v", err)
		log.Printf("MongoDB features will be disabled. Please check your MongoDB configuration.")
		dm.MongoAvailable = false
		return
	}

	dm.MongoDB = mongoDB
	dm.MongoAvailable = true

	// Create MongoDB indexes
	if err := config.CreateMongoIndexes(mongoDB); err != nil {
		log.Printf("Warning: Failed to create MongoDB indexes: %v", err)
	}

	log.Println("MongoDB initialized successfully")
}

// initializeRepositories creates repository instances
func (dm *DatabaseManager) initializeRepositories() {
	// PostgreSQL repositories
	if dm.PostgresAvailable {
		dm.UsersRepo = repository.NewUserRepository(dm.PostgresDB)
		dm.FilesRepo = repository.NewFilesRepository(dm.PostgresDB)
		dm.RecorderRepo = repository.NewRecorderRepository(dm.PostgresDB)
		dm.CodecSettingsRepo = repository.NewCodecSettingsRepository(dm.PostgresDB)
		dm.GeneralSettingsRepo = repository.NewGeneralSettingsRepository(dm.PostgresDB)
	}

	// MongoDB repositories
	if dm.MongoAvailable {
		dm.MongoAnalyticsRepo = repository.NewMongoAnalyticsRepository(dm.MongoDB)
		dm.MongoLogsRepo = repository.NewMongoLogsRepository(dm.MongoDB)
	}
}

// GetPostgresDB returns the PostgreSQL database connection
func (dm *DatabaseManager) GetPostgresDB() *sql.DB {
	return dm.PostgresDB
}

// GetMongoDB returns the MongoDB database connection
func (dm *DatabaseManager) GetMongoDB() *mongo.Database {
	return dm.MongoDB
}

// IsPostgresAvailable returns true if PostgreSQL is available
func (dm *DatabaseManager) IsPostgresAvailable() bool {
	return dm.PostgresAvailable
}

// IsMongoAvailable returns true if MongoDB is available
func (dm *DatabaseManager) IsMongoAvailable() bool {
	return dm.MongoAvailable
}

// GetUsersRepo returns the users repository
func (dm *DatabaseManager) GetUsersRepo() *repository.UserRepository {
	return dm.UsersRepo
}

// GetFilesRepo returns the files repository
func (dm *DatabaseManager) GetFilesRepo() *repository.FilesRepository {
	return dm.FilesRepo
}

// GetRecorderRepo returns the recorder repository
func (dm *DatabaseManager) GetRecorderRepo() *repository.RecorderRepository {
	return dm.RecorderRepo
}

// GetCodecSettingsRepo returns the codec settings repository
func (dm *DatabaseManager) GetCodecSettingsRepo() *repository.CodecSettingsRepository {
	return dm.CodecSettingsRepo
}

// GetGeneralSettingsRepo returns the general settings repository
func (dm *DatabaseManager) GetGeneralSettingsRepo() *repository.GeneralSettingsRepository {
	return dm.GeneralSettingsRepo
}

// GetMongoAnalyticsRepo returns the MongoDB analytics repository
func (dm *DatabaseManager) GetMongoAnalyticsRepo() *repository.MongoAnalyticsRepository {
	if !dm.MongoAvailable {
		return nil
	}
	return dm.MongoAnalyticsRepo
}

// GetMongoLogsRepo returns the MongoDB logs repository
func (dm *DatabaseManager) GetMongoLogsRepo() *repository.MongoLogsRepository {
	if !dm.MongoAvailable {
		return nil
	}
	return dm.MongoLogsRepo
}

// Close closes all database connections
func (dm *DatabaseManager) Close() error {
	var lastErr error

	// Close PostgreSQL
	if dm.PostgresDB != nil {
		if err := dm.PostgresDB.Close(); err != nil {
			log.Printf("Error closing PostgreSQL: %v", err)
			lastErr = err
		}
	}

	// Close MongoDB
	if dm.MongoAvailable {
		if err := config.CloseMongoDB(); err != nil {
			log.Printf("Error closing MongoDB: %v", err)
			lastErr = err
		}
	}

	return lastErr
}

// HealthCheck performs health checks on both databases
func (dm *DatabaseManager) HealthCheck() map[string]bool {
	health := make(map[string]bool)

	// Check PostgreSQL
	if dm.PostgresDB != nil {
		err := dm.PostgresDB.Ping()
		health["postgresql"] = err == nil
	} else {
		health["postgresql"] = false
	}

	// Check MongoDB
	if dm.MongoDB != nil {
		err := dm.MongoDB.Client().Ping(nil, nil)
		health["mongodb"] = err == nil
	} else {
		health["mongodb"] = false
	}

	return health
}

// GetDatabaseInfo returns information about the database setup
func (dm *DatabaseManager) GetDatabaseInfo() map[string]interface{} {
	info := make(map[string]interface{})

	info["postgresql_available"] = dm.PostgresAvailable
	info["mongodb_available"] = dm.MongoAvailable

	if dm.PostgresAvailable {
		info["postgresql_status"] = "connected"
	} else {
		info["postgresql_status"] = "disconnected"
	}

	if dm.MongoAvailable {
		info["mongodb_status"] = "connected"
	} else {
		info["mongodb_status"] = "disconnected"
	}

	return info
}

// Global database manager instance
var globalDatabaseManager *DatabaseManager

// InitGlobalDatabaseManager initializes the global database manager
func InitGlobalDatabaseManager() error {
	globalDatabaseManager = NewDatabaseManager()
	return globalDatabaseManager.Initialize()
}

// GetGlobalDatabaseManager returns the global database manager instance
func GetGlobalDatabaseManager() *DatabaseManager {
	return globalDatabaseManager
}

// CloseGlobalDatabaseManager closes the global database manager
func CloseGlobalDatabaseManager() error {
	if globalDatabaseManager != nil {
		return globalDatabaseManager.Close()
	}
	return nil
}
