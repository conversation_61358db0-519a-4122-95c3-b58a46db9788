package database

import (
	"net/http"
	"showfer-web/service/database_manager"

	"github.com/gin-gonic/gin"
)

// DatabaseStatusResponse represents the database status response
type DatabaseStatusResponse struct {
	Status    string                 `json:"status"`
	Databases map[string]interface{} `json:"databases"`
	Health    map[string]bool        `json:"health"`
	Message   string                 `json:"message"`
}

// GetDatabaseStatus returns the status of both PostgreSQL and MongoDB
// @Summary Get database status
// @Description Returns the connection status and health of PostgreSQL and MongoDB databases
// @Tags database
// @Accept json
// @Produce json
// @Success 200 {object} DatabaseStatusResponse
// @Failure 500 {object} map[string]string
// @Router /api/database/status [get]
func GetDatabaseStatus(c *gin.Context) {
	dbManager := database_manager.GetGlobalDatabaseManager()
	if dbManager == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database manager not initialized",
		})
		return
	}

	// Get database info and health
	dbInfo := dbManager.GetDatabaseInfo()
	health := dbManager.HealthCheck()

	// Determine overall status
	status := "healthy"
	message := "All databases are operational"

	postgresHealthy := health["postgresql"]
	mongoHealthy := health["mongodb"]

	if !postgresHealthy && !mongoHealthy {
		status = "critical"
		message = "Both databases are unavailable"
	} else if !postgresHealthy {
		status = "degraded"
		message = "PostgreSQL is unavailable, MongoDB is operational"
	} else if !mongoHealthy {
		status = "degraded"
		message = "MongoDB is unavailable, PostgreSQL is operational"
	}

	response := DatabaseStatusResponse{
		Status:    status,
		Databases: dbInfo,
		Health:    health,
		Message:   message,
	}

	// Set appropriate HTTP status code
	httpStatus := http.StatusOK
	if status == "critical" {
		httpStatus = http.StatusServiceUnavailable
	} else if status == "degraded" {
		httpStatus = http.StatusPartialContent
	}

	c.JSON(httpStatus, response)
}

// DatabaseConfigResponse represents the database configuration response
type DatabaseConfigResponse struct {
	PostgreSQL map[string]interface{} `json:"postgresql"`
	MongoDB    map[string]interface{} `json:"mongodb"`
}

// GetDatabaseConfig returns the database configuration (without sensitive data)
// @Summary Get database configuration
// @Description Returns the database configuration for PostgreSQL and MongoDB (without passwords)
// @Tags database
// @Accept json
// @Produce json
// @Success 200 {object} DatabaseConfigResponse
// @Router /api/database/config [get]
func GetDatabaseConfig(c *gin.Context) {
	// Get PostgreSQL config (without password)
	pgConfig := map[string]interface{}{
		"host":     "localhost", // You might want to get this from actual config
		"port":     "5432",
		"database": "showfer",
		"ssl_mode": "disable",
	}

	// Get MongoDB config (without password)
	mongoConfig := map[string]interface{}{
		"host":     "localhost",
		"port":     "27017",
		"database": "showfer_mongo",
	}

	response := DatabaseConfigResponse{
		PostgreSQL: pgConfig,
		MongoDB:    mongoConfig,
	}

	c.JSON(http.StatusOK, response)
}

// DatabaseMigrationStatus represents migration status
type DatabaseMigrationStatus struct {
	PostgreSQL map[string]interface{} `json:"postgresql"`
	MongoDB    map[string]interface{} `json:"mongodb"`
	Suggestions []string              `json:"suggestions"`
}

// GetMigrationStatus provides information about what data could be migrated to MongoDB
// @Summary Get migration status
// @Description Returns information about potential data migration from PostgreSQL to MongoDB
// @Tags database
// @Accept json
// @Produce json
// @Success 200 {object} DatabaseMigrationStatus
// @Router /api/database/migration-status [get]
func GetMigrationStatus(c *gin.Context) {
	dbManager := database_manager.GetGlobalDatabaseManager()
	if dbManager == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database manager not initialized",
		})
		return
	}

	postgresInfo := map[string]interface{}{
		"available": dbManager.IsPostgresAvailable(),
		"tables": []string{
			"users",
			"recorders", 
			"rtp_urls",
			"convert_items",
			"schedules",
			"guides",
			"content_analytics",
			"logs",
		},
	}

	mongoInfo := map[string]interface{}{
		"available": dbManager.IsMongoAvailable(),
		"collections": []string{
			"analytics",
			"logs", 
			"schedules",
			"file_metadata",
			"guides",
			"system_metrics",
			"user_sessions",
			"audit_logs",
		},
	}

	suggestions := []string{
		"Consider migrating 'content_analytics' table to MongoDB for better performance with time-series data",
		"Move 'logs' to MongoDB for flexible metadata storage and better search capabilities",
		"Store 'schedules' with complex JSON structures in MongoDB for easier querying",
		"Use MongoDB for 'file_metadata' to store flexible media file properties",
		"Implement MongoDB for 'system_metrics' to handle high-frequency monitoring data",
	}

	if !dbManager.IsMongoAvailable() {
		suggestions = append([]string{
			"MongoDB is not available. Please configure MongoDB connection to enable advanced features",
		}, suggestions...)
	}

	response := DatabaseMigrationStatus{
		PostgreSQL: postgresInfo,
		MongoDB:    mongoInfo,
		Suggestions: suggestions,
	}

	c.JSON(http.StatusOK, response)
}

// SetupDatabaseRoutes sets up the database-related routes
func SetupDatabaseRoutes(router *gin.RouterGroup) {
	db := router.Group("/database")
	{
		db.GET("/status", GetDatabaseStatus)
		db.GET("/config", GetDatabaseConfig)
		db.GET("/migration-status", GetMigrationStatus)
	}
}
