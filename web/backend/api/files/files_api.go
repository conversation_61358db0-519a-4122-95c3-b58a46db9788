package files

import (
	"database/sql"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"showfer-web/service/storage"
	"showfer-web/service/uploader"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// FilesAPI handles file-related API endpoints
type FilesAPI struct {
	db                *sql.DB
	filesRepo         *repository.FilesRepository
	codecSettingsRepo *repository.CodecSettingsRepository
	baseDir           string
	storage           *storage.BackblazeService
}

// NewFilesAPI creates a new FilesAPI
func NewFilesAPI(db *sql.DB, baseDir string, storageService *storage.BackblazeService) *FilesAPI {
	// Create a data directory if it doesn't exist
	if _, err := os.Stat(baseDir); os.IsNotExist(err) {
		err = os.MkdirAll(baseDir, 0755)
		if err != nil {
			logger.Error("Failed to create data directory: %v", err)
			panic(err)
		}
	}

	return &FilesAPI{
		db:                db,
		filesRepo:         repository.NewFilesRepository(db),
		codecSettingsRepo: repository.NewCodecSettingsRepository(db),
		baseDir:           baseDir,
		storage:           storageService,
	}
}

// ListBuckets handles GET /api/v1/buckets
func (api *FilesAPI) ListBuckets(c *gin.Context) {
	buckets, err := api.storage.ListBuckets()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list buckets", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, buckets)
}

// GetFiles handles GET /api/v1/files
func (api *FilesAPI) GetFiles(c *gin.Context) {
	// Check if this is a request for Backblaze files specifically
	storageTypeParam := c.DefaultQuery("storage_type", "")
	bucketName := c.DefaultQuery("bucket", "")

	// If bucket is specified, get Backblaze files from database (storage_type = 1)
	if bucketName != "" {
		// Get convert items from database that are stored in Backblaze (storage_type = 1)
		convertItems, err := api.filesRepo.GetConvertItemsByStorageType(int(models.StorageTypeS3))
		if err != nil {
			logger.Error("Failed to get convert items from database: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get files from database", "details": err.Error()})
			return
		}

		// Filter by bucket name if needed (stored in location field for Backblaze files)
		var filteredItems []models.ConvertItem
		for _, item := range convertItems {
			// For now, return all Backblaze files. You can add bucket filtering logic here
			// if you store bucket info in a specific field
			filteredItems = append(filteredItems, item)
		}

		c.JSON(http.StatusOK, filteredItems)
		return
	}

	// If storage_type is specified, filter by that
	if storageTypeParam != "" {
		storageType, err := strconv.Atoi(storageTypeParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid storage_type parameter"})
			return
		}

		convertItems, err := api.filesRepo.GetConvertItemsByStorageType(storageType)
		if err != nil {
			logger.Error("Failed to get convert items by storage type: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get files", "details": err.Error()})
			return
		}

		c.JSON(http.StatusOK, convertItems)
		return
	}

	// Default: Get all files with pagination
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))

	pagination := models.Pagination{
		Page:  page,
		Limit: limit,
	}

	result, err := api.filesRepo.ListConvertItems(pagination)
	if err != nil {
		logger.Error("Failed to get convert items: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get files", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetFilesByBucket handles GET /api/v1/files/bucket/:bucket
func (api *FilesAPI) GetFilesByBucket(c *gin.Context) {
	bucketName := c.Param("bucket")
	if bucketName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Bucket name is required"})
		return
	}

	// Get convert items from database that are stored in Backblaze (storage_type = 1)
	convertItems, err := api.filesRepo.GetConvertItemsByStorageType(int(models.StorageTypeS3))
	if err != nil {
		logger.Error("Failed to get convert items from database: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get files from database", "details": err.Error()})
		return
	}

	// Filter by bucket name
	var filteredItems []models.ConvertItem
	for _, item := range convertItems {
		// If bucket name is stored and matches, or if no bucket name is stored (legacy files)
		if item.BucketName == nil || *item.BucketName == bucketName {
			filteredItems = append(filteredItems, item)
		}
	}

	c.JSON(http.StatusOK, filteredItems)
}

// GetFilesByLocation handles GET /api/v1/files/location/:location
func (api *FilesAPI) GetFilesByLocation(c *gin.Context) {
	location := c.Param("location")

	// Handle special "root" location
	if location == "root" {
		location = "/"
	} else {
		// Replace underscores with slashes for nested paths
		location = strings.ReplaceAll(location, "_", "/")

		// Handle case where we might have double slashes
		for strings.Contains(location, "//") {
			location = strings.ReplaceAll(location, "//", "/")
		}

		// Ensure location starts with /
		if !strings.HasPrefix(location, "/") {
			location = "/" + location
		}

		// Ensure location ends with /
		if !strings.HasSuffix(location, "/") && location != "/" {
			location = location + "/"
		}
	}

	// Get files by location
	files, err := api.filesRepo.ConvertItemsByFolder(location)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get files by location", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, files)
}

// GetFoldersByLocation handles GET /api/v1/files/folders/:location
func (api *FilesAPI) GetFoldersByLocation(c *gin.Context) {
	location := c.Param("location")

	// Handle special "root" location
	if location == "root" {
		location = "/"
	} else {
		// Replace underscores with slashes for nested paths
		location = strings.ReplaceAll(location, "_", "/")

		// Handle case where we might have double slashes
		for strings.Contains(location, "//") {
			location = strings.ReplaceAll(location, "//", "/")
		}

		// Ensure location starts with /
		if !strings.HasPrefix(location, "/") {
			location = "/" + location
		}

		// Ensure location ends with /
		if !strings.HasSuffix(location, "/") && location != "/" {
			location = location + "/"
		}
	}

	// Get folders by location
	folders, err := api.filesRepo.GetSubfolders(location)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get folders by location", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, folders)
}

// UploadFiles handles POST /api/v1/files/upload
func (api *FilesAPI) UploadFiles(c *gin.Context) {
	bucketName := c.DefaultPostForm("bucket", os.Getenv("B2_DEFAULT_BUCKET"))
	if bucketName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Bucket name is required"})
		return
	}

	location := c.PostForm("location")
	if location == "" {
		location = "/"
	}

	// Ensure location starts with /
	if !strings.HasPrefix(location, "/") {
		location = "/" + location
	}

	// Ensure location ends with /
	if !strings.HasSuffix(location, "/") && location != "/" {
		location = location + "/"
	}

	// Create temporary folder for processing
	tempDir := filepath.Join(api.baseDir, "temp")
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create temp directory", "details": err.Error()})
		return
	}

	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse form", "details": err.Error()})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No files uploaded"})
		return
	}

	var uploadedFiles []storage.FileInfo
	for _, file := range files {
		// Save file temporarily
		filename := filepath.Base(file.Filename)
		tempPath := filepath.Join(tempDir, filename)

		if err := c.SaveUploadedFile(file, tempPath); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file", "details": err.Error()})
			return
		}

		// Process file metadata with Backblaze storage type
		err := uploader.ProcessFile(tempPath, location, api.baseDir, api.filesRepo, api.codecSettingsRepo, models.StorageTypeS3, bucketName)
		if err != nil {
			logger.Error("Failed to process file %s: %v", filename, err)
			os.Remove(tempPath)
			continue
		}

		// Upload to Backblaze
		fileInfo, err := api.storage.UploadFile(bucketName, tempPath, location)
		if err != nil {
			logger.Error("Failed to upload file %s to Backblaze: %v", filename, err)
			os.Remove(tempPath)
			continue
		}

		uploadedFiles = append(uploadedFiles, *fileInfo)

		// Clean up temp file
		os.Remove(tempPath)
	}

	c.JSON(http.StatusOK, gin.H{
		"message": fmt.Sprintf("Successfully uploaded %d files", len(uploadedFiles)),
		"files":   uploadedFiles,
	})
}

// DeleteFile handles DELETE /api/v1/files/:id
func (api *FilesAPI) DeleteFile(c *gin.Context) {
	bucketName := c.DefaultQuery("bucket", os.Getenv("B2_DEFAULT_BUCKET"))
	if bucketName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Bucket name is required"})
		return
	}

	fileName := c.Param("id")
	if fileName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File name is required"})
		return
	}

	if err := api.storage.DeleteFile(bucketName, fileName); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete file", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "File deleted successfully"})
}

// UpdateFile handles PUT /api/v1/files/:id
func (api *FilesAPI) UpdateFile(c *gin.Context) {
	// Parse file ID
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// Parse request body
	var input models.ConvertItemUpdateInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Get file by ID
	file, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
		}
		return
	}

	// Update file
	file.Name = input.Name
	file.Description = input.Description
	file.Episode = input.Episode

	err = api.filesRepo.UpdateConvertItem(file)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update file", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, file)
}

// RenameFile handles PUT /api/v1/files/:id/rename
func (api *FilesAPI) RenameFile(c *gin.Context) {
	// Parse file ID
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// Parse request body
	var input struct {
		NewFilename string `json:"new_filename"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Validate new filename
	if input.NewFilename == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "New filename cannot be empty"})
		return
	}

	// Get file by ID
	file, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
		}
		return
	}

	// Rename file on disk and update database
	err = api.filesRepo.RenameFile(file, input.NewFilename, api.baseDir)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to rename file", "details": err.Error()})
		return
	}

	// Get updated file
	updatedFile, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get updated file", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedFile)
}

// DownloadFile handles GET /api/v1/files/:id/download
func (api *FilesAPI) DownloadFile(c *gin.Context) {
	// Parse file ID
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// Get file by ID
	file, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
		}
		return
	}

	// Handle different storage types
	if file.StorageType == int(models.StorageTypeS3) {
		// Backblaze/S3 storage - redirect to cloud URL
		if api.storage == nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Cloud storage not configured"})
			return
		}

		// Get bucket name from file record or use default
		bucketName := ""
		if file.BucketName != nil {
			bucketName = *file.BucketName
		} else {
			bucketName = os.Getenv("B2_DEFAULT_BUCKET")
		}

		if bucketName == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "No bucket specified for cloud file"})
			return
		}

		// Get download URL from Backblaze
		downloadURL, err := api.storage.GetDownloadURL(bucketName, filepath.Join(file.Location, file.Filename))
		if err != nil {
			logger.Error("Failed to get download URL for cloud file: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get download URL", "details": err.Error()})
			return
		}

		// Redirect to the cloud download URL
		c.Redirect(http.StatusTemporaryRedirect, downloadURL)
		return
	}

	// Local storage - serve file directly
	filePath := filepath.Join(api.baseDir, file.Location, file.Filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found on disk"})
		return
	}

	// Set appropriate headers for file download
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", file.Filename))
	c.Header("Content-Type", "application/octet-stream")
	c.File(filePath)
}

// StreamFile handles GET /api/v1/files/:id/stream - for video playback
func (api *FilesAPI) StreamFile(c *gin.Context) {
	// Parse file ID
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// Get file by ID
	file, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
		}
		return
	}

	// Handle different storage types
	if file.StorageType == int(models.StorageTypeS3) {
		// Backblaze/S3 storage - redirect to cloud URL
		if api.storage == nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Cloud storage not configured"})
			return
		}

		// Get bucket name from file record or use default
		bucketName := ""
		if file.BucketName != nil {
			bucketName = *file.BucketName
		} else {
			bucketName = os.Getenv("B2_DEFAULT_BUCKET")
		}

		if bucketName == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "No bucket specified for cloud file"})
			return
		}

		// Get streaming URL from Backblaze
		streamURL, err := api.storage.GetDownloadURL(bucketName, filepath.Join(file.Location, file.Filename))
		if err != nil {
			logger.Error("Failed to get stream URL for cloud file: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get stream URL", "details": err.Error()})
			return
		}

		// Redirect to the cloud streaming URL
		c.Redirect(http.StatusTemporaryRedirect, streamURL)
		return
	}

	// Local storage - serve file directly with appropriate headers for streaming
	filePath := filepath.Join(api.baseDir, file.Location, file.Filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found on disk"})
		return
	}

	// Set appropriate headers for video streaming
	c.Header("Content-Type", "video/mp4") // Adjust based on file type
	c.Header("Accept-Ranges", "bytes")
	c.Header("Cache-Control", "no-cache")
	c.File(filePath)
}

// MoveFiles handles POST /api/v1/files/move
func (api *FilesAPI) MoveFiles(c *gin.Context) {
	// Parse request body
	var input struct {
		FileIDs     []int64 `json:"file_ids"`
		Destination string  `json:"destination"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Validate input
	if len(input.FileIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file IDs provided"})
		return
	}

	// Ensure destination starts with /
	if !strings.HasPrefix(input.Destination, "/") {
		input.Destination = "/" + input.Destination
	}

	// Ensure destination ends with /
	if !strings.HasSuffix(input.Destination, "/") && input.Destination != "/" {
		input.Destination = input.Destination + "/"
	}

	// Create destination folder if it doesn't exist
	destPath := filepath.Join(api.baseDir, input.Destination)
	if _, err := os.Stat(destPath); os.IsNotExist(err) {
		err = os.MkdirAll(destPath, 0755)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create destination folder", "details": err.Error()})
			return
		}
	}

	// Process each file
	failedCount := 0
	errors := []string{}
	successCount := 0

	for _, fileID := range input.FileIDs {
		// Get file by ID
		file, err := api.filesRepo.GetConvertItemById(fileID)
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("Failed to get file with ID %d: %v", fileID, err))
			continue
		}

		// Skip files that are being processed
		if file.Status == int(models.FileStatusQueue) || file.Status == int(models.FileStatusProcessing) {
			failedCount++
			errors = append(errors, fmt.Sprintf("File %s is currently being processed and cannot be moved", file.Filename))
			continue
		}

		// Check if source and destination are the same
		if file.Location == input.Destination {
			failedCount++
			errors = append(errors, fmt.Sprintf("File %s is already in the destination folder", file.Filename))
			continue
		}

		// Check if a file with the same name already exists in the destination
		destFilePath := filepath.Join(destPath, file.Filename)
		if _, err := os.Stat(destFilePath); err == nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("A file with the name %s already exists in the destination folder", file.Filename))
			continue
		}

		// Move file on disk
		sourcePath := filepath.Join(api.baseDir, file.Location, file.Filename)
		err = os.Rename(sourcePath, destFilePath)
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("Failed to move file %s: %v", file.Filename, err))
			continue
		}

		// Update file location in database
		oldLocation := file.Location

		// Use the new UpdateFileLocation function to update both location and c_location
		// and also update any references in schedules
		err = api.filesRepo.UpdateFileLocation(file.ID, oldLocation, input.Destination)
		if err != nil {
			// Try to move the file back to its original location
			os.Rename(destFilePath, sourcePath)

			failedCount++
			errors = append(errors, fmt.Sprintf("Failed to update file %s in database: %v", file.Filename, err))
			continue
		}

		// Update the in-memory file object to reflect the new location
		file.Location = input.Destination
		file.CLocation = input.Destination

		successCount++
		logger.Log("Moved file %s from %s to %s", file.Filename, oldLocation, input.Destination)
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      failedCount == 0,
		"failedCount":  failedCount,
		"successCount": successCount,
		"errors":       errors,
	})
}

// GetFileInfoById handles GET /api/v1/files/:id
func (api *FilesAPI) GetFileInfoById(c *gin.Context) {
	// Parse file ID
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// Get file by ID
	file, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, file)
}
