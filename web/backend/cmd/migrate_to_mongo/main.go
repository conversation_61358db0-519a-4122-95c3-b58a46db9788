package main

import (
	"database/sql"
	"flag"
	"fmt"
	"log"
	"showfer-web/config"
	"showfer-web/models"
	"showfer-web/repository"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func main() {
	var (
		dryRun = flag.Bool("dry-run", false, "Show what would be migrated without actually doing it")
		table  = flag.String("table", "", "Specific table to migrate (analytics, logs, schedules)")
		limit  = flag.Int("limit", 1000, "Number of records to migrate per batch")
	)
	flag.Parse()

	if *dryRun {
		fmt.Println("DRY RUN MODE - No data will be migrated")
		fmt.Println("=====================================")
	}

	// Initialize PostgreSQL
	postgresDB, err := config.InitDB()
	if err != nil {
		log.Fatalf("Failed to initialize PostgreSQL: %v", err)
	}
	defer postgresDB.Close()

	// Initialize MongoDB
	mongoDB, err := config.InitMongoDB()
	if err != nil {
		log.Fatalf("Failed to initialize MongoDB: %v", err)
	}
	defer config.CloseMongoDB()

	// Create MongoDB indexes
	if err := config.CreateMongoIndexes(mongoDB); err != nil {
		log.Printf("Warning: Failed to create MongoDB indexes: %v", err)
	}

	// Determine what to migrate
	tablesToMigrate := []string{}
	if *table != "" {
		tablesToMigrate = []string{*table}
	} else {
		tablesToMigrate = []string{"analytics", "logs"}
	}

	for _, tableName := range tablesToMigrate {
		fmt.Printf("\n=== Migrating %s ===\n", tableName)

		switch tableName {
		case "analytics":
			if err := migrateAnalytics(postgresDB, mongoDB, *dryRun, *limit); err != nil {
				log.Printf("Error migrating analytics: %v", err)
			}
		case "logs":
			if err := migrateLogs(postgresDB, mongoDB, *dryRun, *limit); err != nil {
				log.Printf("Error migrating logs: %v", err)
			}
		case "schedules":
			if err := migrateSchedules(postgresDB, mongoDB, *dryRun, *limit); err != nil {
				log.Printf("Error migrating schedules: %v", err)
			}
		default:
			log.Printf("Unknown table: %s", tableName)
		}
	}

	fmt.Println("\nMigration completed!")
}

func migrateAnalytics(postgresDB *sql.DB, mongoDB *mongo.Database, dryRun bool, limit int) error {
	// Check if content_analytics table exists
	var exists bool
	err := postgresDB.QueryRow(`
		SELECT EXISTS (
			SELECT FROM information_schema.tables 
			WHERE table_schema = 'public' 
			AND table_name = 'content_analytics'
		)
	`).Scan(&exists)

	if err != nil || !exists {
		fmt.Println("content_analytics table does not exist in PostgreSQL")
		return nil
	}

	// Count total records
	var totalCount int
	err = postgresDB.QueryRow("SELECT COUNT(*) FROM content_analytics").Scan(&totalCount)
	if err != nil {
		return fmt.Errorf("failed to count analytics records: %w", err)
	}

	fmt.Printf("Found %d analytics records to migrate\n", totalCount)

	if dryRun {
		fmt.Printf("Would migrate %d analytics records to MongoDB\n", totalCount)
		return nil
	}

	// Initialize MongoDB repository
	mongoAnalyticsRepo := repository.NewMongoAnalyticsRepository(mongoDB)

	// Migrate in batches
	offset := 0
	migrated := 0

	for offset < totalCount {
		rows, err := postgresDB.Query(`
			SELECT id, schedule_id, item_id, content_name, content_path, 
				   rtp_output, played_at, duration, play_type
			FROM content_analytics 
			ORDER BY id 
			LIMIT $1 OFFSET $2
		`, limit, offset)

		if err != nil {
			return fmt.Errorf("failed to query analytics: %w", err)
		}

		for rows.Next() {
			var analytics models.MongoContentAnalytics
			var itemID sql.NullInt64
			var playedAtStr string

			err := rows.Scan(
				&analytics.ID,
				&analytics.ScheduleID,
				&itemID,
				&analytics.ContentName,
				&analytics.ContentPath,
				&analytics.RTPOutput,
				&playedAtStr,
				&analytics.Duration,
				&analytics.PlayType,
			)
			if err != nil {
				log.Printf("Error scanning analytics row: %v", err)
				continue
			}

			// Handle nullable item_id
			if itemID.Valid {
				analytics.ItemID = &itemID.Int64
			}

			// Parse played_at timestamp
			analytics.PlayedAt, err = time.Parse(time.RFC3339, playedAtStr)
			if err != nil {
				log.Printf("Error parsing played_at timestamp: %v", err)
				continue
			}

			// Generate new MongoDB ObjectID
			analytics.ID = primitive.NewObjectID()

			// Create in MongoDB
			if err := mongoAnalyticsRepo.Create(&analytics); err != nil {
				log.Printf("Error creating analytics in MongoDB: %v", err)
				continue
			}

			migrated++
		}
		rows.Close()

		offset += limit
		fmt.Printf("Migrated %d/%d analytics records\n", migrated, totalCount)
	}

	fmt.Printf("Successfully migrated %d analytics records\n", migrated)
	return nil
}

func migrateLogs(postgresDB *sql.DB, mongoDB *mongo.Database, dryRun bool, limit int) error {
	// Check if logs table exists
	var exists bool
	err := postgresDB.QueryRow(`
		SELECT EXISTS (
			SELECT FROM information_schema.tables 
			WHERE table_schema = 'public' 
			AND table_name = 'logs'
		)
	`).Scan(&exists)

	if err != nil || !exists {
		fmt.Println("logs table does not exist in PostgreSQL")
		return nil
	}

	// Count total records
	var totalCount int
	err = postgresDB.QueryRow("SELECT COUNT(*) FROM logs").Scan(&totalCount)
	if err != nil {
		return fmt.Errorf("failed to count log records: %w", err)
	}

	fmt.Printf("Found %d log records to migrate\n", totalCount)

	if dryRun {
		fmt.Printf("Would migrate %d log records to MongoDB\n", totalCount)
		return nil
	}

	// Initialize MongoDB repository
	mongoLogsRepo := repository.NewMongoLogsRepository(mongoDB)

	// Migrate in batches
	offset := 0
	migrated := 0

	for offset < totalCount {
		rows, err := postgresDB.Query(`
			SELECT id, timestamp, level, message, source, user_id, 
				   request_id, metadata, created_at
			FROM logs 
			ORDER BY id 
			LIMIT $1 OFFSET $2
		`, limit, offset)

		if err != nil {
			return fmt.Errorf("failed to query logs: %w", err)
		}

		for rows.Next() {
			var logEntry models.MongoLogEntry
			var userID sql.NullInt64
			var requestID sql.NullString
			var metadata sql.NullString

			err := rows.Scan(
				&logEntry.ID,
				&logEntry.Timestamp,
				&logEntry.Level,
				&logEntry.Message,
				&logEntry.Source,
				&userID,
				&requestID,
				&metadata,
				&logEntry.CreatedAt,
			)
			if err != nil {
				log.Printf("Error scanning log row: %v", err)
				continue
			}

			// Handle nullable fields
			if userID.Valid {
				logEntry.UserID = &userID.Int64
			}
			if requestID.Valid {
				logEntry.RequestID = &requestID.String
			}

			// Generate new MongoDB ObjectID
			logEntry.ID = primitive.NewObjectID()

			// Create in MongoDB
			if err := mongoLogsRepo.Create(&logEntry); err != nil {
				log.Printf("Error creating log in MongoDB: %v", err)
				continue
			}

			migrated++
		}
		rows.Close()

		offset += limit
		fmt.Printf("Migrated %d/%d log records\n", migrated, totalCount)
	}

	fmt.Printf("Successfully migrated %d log records\n", migrated)
	return nil
}

func migrateSchedules(postgresDB *sql.DB, mongoDB *mongo.Database, dryRun bool, limit int) error {
	fmt.Println("Schedule migration not yet implemented")
	fmt.Println("Schedules contain complex JSON structures that require custom migration logic")
	return nil
}
