package repository

import (
	"context"
	"fmt"
	"showfer-web/models"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type MongoAnalyticsRepository struct {
	collection *mongo.Collection
}

func NewMongoAnalyticsRepository(db *mongo.Database) *MongoAnalyticsRepository {
	return &MongoAnalyticsRepository{
		collection: db.Collection(models.CollectionAnalytics),
	}
}

// Create inserts a new analytics entry
func (r *MongoAnalyticsRepository) Create(analytics *models.MongoContentAnalytics) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	analytics.CreatedAt = time.Now().UTC()
	analytics.UpdatedAt = time.Now().UTC()

	result, err := r.collection.InsertOne(ctx, analytics)
	if err != nil {
		return fmt.Errorf("failed to create analytics entry: %w", err)
	}

	analytics.ID = result.InsertedID.(primitive.ObjectID)
	return nil
}

// GetByID retrieves an analytics entry by ID
func (r *MongoAnalyticsRepository) GetByID(id primitive.ObjectID) (*models.MongoContentAnalytics, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var analytics models.MongoContentAnalytics
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&analytics)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("analytics entry not found")
		}
		return nil, fmt.Errorf("failed to get analytics entry: %w", err)
	}

	return &analytics, nil
}

// GetByScheduleID retrieves analytics entries for a specific schedule
func (r *MongoAnalyticsRepository) GetByScheduleID(scheduleID int64, limit, offset int) ([]models.MongoContentAnalytics, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	filter := bson.M{"schedule_id": scheduleID}
	opts := options.Find().
		SetSort(bson.D{{Key: "played_at", Value: -1}}).
		SetLimit(int64(limit)).
		SetSkip(int64(offset))

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find analytics entries: %w", err)
	}
	defer cursor.Close(ctx)

	var analytics []models.MongoContentAnalytics
	if err = cursor.All(ctx, &analytics); err != nil {
		return nil, fmt.Errorf("failed to decode analytics entries: %w", err)
	}

	return analytics, nil
}

// GetByDateRange retrieves analytics entries within a date range
func (r *MongoAnalyticsRepository) GetByDateRange(startDate, endDate time.Time, limit, offset int) ([]models.MongoContentAnalytics, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	filter := bson.M{
		"played_at": bson.M{
			"$gte": startDate,
			"$lte": endDate,
		},
	}

	opts := options.Find().
		SetSort(bson.D{{Key: "played_at", Value: -1}}).
		SetLimit(int64(limit)).
		SetSkip(int64(offset))

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find analytics entries: %w", err)
	}
	defer cursor.Close(ctx)

	var analytics []models.MongoContentAnalytics
	if err = cursor.All(ctx, &analytics); err != nil {
		return nil, fmt.Errorf("failed to decode analytics entries: %w", err)
	}

	return analytics, nil
}

// GetTopContent retrieves the most played content
func (r *MongoAnalyticsRepository) GetTopContent(limit int) ([]models.ContentAnalyticsSummary, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	pipeline := []bson.M{
		{
			"$group": bson.M{
				"_id": bson.M{
					"content_name": "$content_name",
					"content_path": "$content_path",
				},
				"total_plays":     bson.M{"$sum": 1},
				"total_duration":  bson.M{"$sum": "$duration"},
				"last_played_at":  bson.M{"$max": "$played_at"},
				"output_channels": bson.M{"$addToSet": "$rtp_output"},
			},
		},
		{
			"$sort": bson.M{"total_plays": -1},
		},
		{
			"$limit": limit,
		},
		{
			"$project": bson.M{
				"content_name":    "$_id.content_name",
				"content_path":    "$_id.content_path",
				"total_plays":     1,
				"total_duration":  1,
				"last_played_at":  1,
				"output_channels": 1,
				"_id":             0,
			},
		},
	}

	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to aggregate top content: %w", err)
	}
	defer cursor.Close(ctx)

	var results []models.ContentAnalyticsSummary
	if err = cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode top content: %w", err)
	}

	return results, nil
}

// GetAnalyticsStats retrieves aggregate statistics
func (r *MongoAnalyticsRepository) GetAnalyticsStats(filter bson.M) (*models.AnalyticsStats, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	pipeline := []bson.M{
		{"$match": filter},
		{
			"$group": bson.M{
				"_id":            nil,
				"total_plays":    bson.M{"$sum": 1},
				"total_duration": bson.M{"$sum": "$duration"},
				"unique_outputs": bson.M{"$addToSet": "$rtp_output"},
			},
		},
		{
			"$project": bson.M{
				"total_plays":    1,
				"total_duration": 1,
				"unique_outputs": bson.M{"$size": "$unique_outputs"},
				"_id":            0,
			},
		},
	}

	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to aggregate analytics stats: %w", err)
	}
	defer cursor.Close(ctx)

	var stats []models.AnalyticsStats
	if err = cursor.All(ctx, &stats); err != nil {
		return nil, fmt.Errorf("failed to decode analytics stats: %w", err)
	}

	if len(stats) == 0 {
		return &models.AnalyticsStats{}, nil
	}

	return &stats[0], nil
}

// Delete removes an analytics entry
func (r *MongoAnalyticsRepository) Delete(id primitive.ObjectID) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return fmt.Errorf("failed to delete analytics entry: %w", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("analytics entry not found")
	}

	return nil
}

// DeleteOldEntries removes analytics entries older than the specified duration
func (r *MongoAnalyticsRepository) DeleteOldEntries(olderThan time.Duration) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	cutoffDate := time.Now().UTC().Add(-olderThan)
	filter := bson.M{
		"played_at": bson.M{
			"$lt": cutoffDate,
		},
	}

	result, err := r.collection.DeleteMany(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to delete old analytics entries: %w", err)
	}

	return result.DeletedCount, nil
}

// Count returns the total number of analytics entries matching the filter
func (r *MongoAnalyticsRepository) Count(filter bson.M) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	count, err := r.collection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to count analytics entries: %w", err)
	}

	return count, nil
}
